"""
应用配置管理模块
"""
import json
import os
from typing import List, Dict, Any, Optional
from pydantic_settings import BaseSettings
from pydantic import Field, validator
from functools import lru_cache
from functools import cached_property

try:
    import tiktoken  # type: ignore
except Exception:  # pragma: no cover
    tiktoken = None  # fallback if not installed


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = Field(default="Web Assistant Agents", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")

    # 服务配置
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=4, env="WORKERS")

    # 自定义LLM API配置
    custom_llm_api_url: str = Field(
        default="http://*************/web/unauth/LLM_api_proxy/v1/chat/completions",
        env="CUSTOM_LLM_API_URL"
    )
    default_llm_model: str = Field(default="ht::saas-deepseek-v3", env="DEFAULT_LLM_MODEL")

    # 可用模型列表
    available_models: List[str] = Field(
        default_factory=lambda: [
            "ht::saas-deepseek-v3",
            "ht::saas-deepseek-r1",
            "ht::saas-doubao-15-pro-32k",
            "ht::saas-deepseek-r1-thinking",
            "ht::qwen-25-14b-int4",
            "ht::qwen-25-14b-int4-noft",
            "ht::local-qwq-32b"
        ],
        env="AVAILABLE_MODELS"
    )

    # 模型上下文窗口和默认生成限制（近似值，避免硬编码在业务逻辑）
    model_context_window: Dict[str, int] = Field(
        default_factory=lambda: {
            "ht::saas-deepseek-v3": 40000,
            "ht::saas-deepseek-r1": 32000,
            "ht::saas-doubao-15-pro-32k": 32000,
            "ht::saas-deepseek-r1-thinking": 32000,
            "ht::qwen-25-14b-int4": 8000,
            "ht::qwen-25-14b-int4-noft": 8000,
            "ht::local-qwq-32b": 32000,
        },
        env="MODEL_CONTEXT_WINDOW"
    )
    default_max_tokens: int = Field(default=1000, env="DEFAULT_MAX_TOKENS")



    # 日志配置
    log_format: str = Field(default="json", env="LOG_FORMAT")

    # 文件上传配置
    max_file_size: int = Field(default=10485760, env="MAX_FILE_SIZE")  # 10MB
    allowed_file_types: List[str] = Field(
        default_factory=lambda: ["jpg", "jpeg", "png", "gif", "pdf", "docx", "txt"],
        env="ALLOWED_FILE_TYPES"
    )

    # 外部服务配置
    web_scraping_timeout: int = Field(default=30, env="WEB_SCRAPING_TIMEOUT")
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    web_search_api_url: str = Field(default="http://10.102.75.112/fst/api/v1/websearch", env="WEB_SEARCH_API_URL")
    image_parse_api_url: str = Field(default="http://10.102.75.112/fst/api/v1/image-parse", env="IMAGE_PARSE_API_URL")

    # 翻译服务配置
    doubao_translation_api_url: str = Field(
        default="http://168.63.65.40:8090/pai/xapi/at/znjqfy/llm_translate_qqt/llm_translate_qqt/process",
        env="DOUBAO_TRANSLATION_API_URL"
    )
    doubao_app_sys_id: str = Field(default="001038", env="DOUBAO_APP_SYS_ID")
    doubao_token: str = Field(default="8f2752d4d31748e5b13102a0402db896", env="DOUBAO_TOKEN")

    # 开发配置
    reload: bool = Field(default=False, env="RELOAD")
    access_log: bool = Field(default=True, env="ACCESS_LOG")

    # 长内容压缩配置
    long_content_merge_reserve_tokens: int = Field(
        default=2000, env="LONG_CONTENT_MERGE_RESERVE_TOKENS"
    )
    long_content_min_chunk_tokens: int = Field(
        default=4000, env="LONG_CONTENT_MIN_CHUNK_TOKENS"
    )

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        env_ignore_empty = True

    def validate_model(self, model: str) -> bool:
        """验证模型是否可用"""
        return model in self.available_models

    def get_context_window(self, model: Optional[str]) -> int:
        """获取模型上下文窗口大小，未知模型使用32k作为保守默认值"""
        model_key = model or self.default_llm_model
        return int(self.model_context_window.get(model_key, 32000))

    def rough_token_count(self, text: str) -> int:
        """粗略估算token数（中文/英文混合场景下的保守估计）"""
        if not text:
            return 0
        # 近似：中文字符≈1.6 token，英文单词≈1.3 token；为简单起见取平均每字符≈0.6 token
        # 为更安全，乘以1.0作为线性近似（可以接入tiktoken进一步精确）
        return int(len(text) * 0.6) + 1

    @cached_property
    def _encoding_default(self):  # lazy init
        if tiktoken is None:
            return None
        # Use cl100k_base as a broad default compatible with many chat models
        try:
            return tiktoken.get_encoding("cl100k_base")
        except Exception:
            return None

    def token_count(self, text: str, model: Optional[str] = None) -> int:
        """优先使用tiktoken更精确地统计token，失败时退回粗略估算"""
        if not text:
            return 0
        if tiktoken is None:
            return self.rough_token_count(text)
        enc = self._encoding_default
        if enc is None:
            return self.rough_token_count(text)
        try:
            return len(enc.encode(text))
        except Exception:
            return self.rough_token_count(text)


@lru_cache()
def get_settings() -> Settings:
    """获取应用配置实例（单例模式）"""
    return Settings()


def validate_config() -> bool:
    """验证配置是否正确"""
    try:
        config = get_settings()
        
        # 验证必要的配置项
        required_configs = [
            'app_name', 'custom_llm_api_url', 'default_llm_model'
        ]
        
        for config_name in required_configs:
            value = getattr(config, config_name, None)
            if not value:
                print(f"错误: 必需的配置项 {config_name} 未设置")
                return False
        
        # 验证模型配置
        if not config.validate_model(config.default_llm_model):
            print(f"警告: 默认模型 {config.default_llm_model} 不在可用模型列表中")
        
        print("配置验证通过")
        return True
        
    except Exception as e:
        print(f"配置验证失败: {e}")
        return False


# 全局配置实例
settings = get_settings()
