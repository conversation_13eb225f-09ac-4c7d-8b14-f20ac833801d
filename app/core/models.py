"""
核心数据模型定义
"""
from typing import Any, Dict, Optional
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field
import uuid


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentType(str, Enum):
    """智能体类型枚举"""
    CONTENT_UNDERSTANDING = "content_understanding"
    GENERAL_QA = "general_qa"
    IMAGE_PARSE = "image_parse"
    TRANSLATION = "translation"
    PROMPT_OPTIMIZER = "prompt_optimizer"



class AgentState(BaseModel):
    """智能体状态模型 - 支持原始LLM响应"""
    agent_id: str
    agent_type: AgentType
    status: TaskStatus
    input_data: Dict[str, Any]
    output_data: Optional[Any] = None  # Changed to Any to support raw LLM responses
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    def update_status(self, status: TaskStatus, output_data: Optional[Any] = None, 
                     error_message: Optional[str] = None):
        """更新状态 - 支持任意类型的输出数据"""
        self.status = status
        self.updated_at = datetime.utcnow()
        if output_data is not None:  # Changed to allow falsy values
            self.output_data = output_data
        if error_message:
            self.error_message = error_message


class ModelConfig(BaseModel):
    """模型配置"""
    model_name: str
    api_key: str
    base_url: Optional[str] = None
    temperature: float = Field(default=0.7, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(default=None, gt=0)
    top_p: float = Field(default=1.0, ge=0.0, le=1.0)
    frequency_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)
    presence_penalty: float = Field(default=0.0, ge=-2.0, le=2.0)


class HealthCheck(BaseModel):
    """健康检查模型"""
    status: str
    version: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    services: Dict[str, str] = Field(default_factory=dict)
    uptime: Optional[float] = None
