"""
Image parsing agent with LangGraph workflow - direct result passthrough
"""
from typing import Op<PERSON>, AsyncGenerator, Union, Dict, Any, List
import json

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.image_parse_service import image_parse_service
from app.utils.logger import get_logger
from app.config import settings

logger = get_logger(__name__)


class ImageParseAgent(BaseAgent):
    """Image parsing agent with direct result passthrough"""

    def __init__(self, agent_type: AgentType):
        super().__init__(
            agent_type=agent_type,
            name="Image Parse Agent",
            description="Processes image URLs and returns parsed content directly from external API"
        )

    def _build_graph(self):
        """Build LangGraph workflow for image parsing"""
        workflow = StateGraph(AgentWorkflowState)

        # Add processing nodes
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("parse_image", self._parse_image_node)
        workflow.add_node("format_result", self._format_result_node)
        workflow.add_node("finalize", self._finalize_node)

        # Define workflow edges
        workflow.set_entry_point("validate_input")
        workflow.add_edge("validate_input", "parse_image")
        workflow.add_edge("parse_image", "format_result")
        workflow.add_edge("format_result", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self.graph = workflow.compile()
        logger.info("LangGraph workflow built for image parse agent")

    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Required by BaseAgent interface"""
        return state

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate and prepare input data for image parsing"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]

            url = input_data.get("url", "")
            if not url:
                raise ValueError("图片URL不能为空")

            # 简单的URL格式验证
            if not (url.startswith('http://') or url.startswith('https://')):
                raise ValueError("图片URL必须以http://或https://开头")

            # Store validated data
            state["step_results"]["url"] = url
            state["step_results"]["question"] = input_data.get("question", "描述这个图片")
            state["step_results"]["is_stream"] = input_data.get("stream", False)
            logger.info(f"Input validated - image URL: {url}, question: {input_data.get('question', '描述这个图片')}, stream: {input_data.get('stream', False)}")

        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state

    async def _parse_image_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Call external image parsing API"""
        try:
            state["current_step"] = "parse_image"
            step_results = state["step_results"]
            url = step_results.get("url", "")
            question = step_results.get("question", "描述这个图片")

            logger.info(f"开始解析图片: {url}, 问题: {question}")
            
            # Call external image parsing service
            parse_result = await image_parse_service.parse_image(url, question)
            
            state["step_results"]["parse_result"] = parse_result
            logger.info(f"图片解析完成: success={parse_result.success}, message={parse_result.message}")

        except Exception as e:
            state["error"] = f"Image parsing failed: {str(e)}"
            logger.error(f"Image parsing failed: {e}")

        return state

    async def _format_result_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Format the parsing result to match content agent output format"""
        try:
            state["current_step"] = "format_result"
            step_results = state["step_results"]
            parse_result = step_results.get("parse_result")
            is_stream = step_results.get("is_stream", False)

            if not parse_result:
                raise ValueError("没有解析结果")

            if parse_result.success:
                import time
                created_time = int(time.time())
                response_id = "image_parse_" + str(abs(hash(parse_result.output)))[:20]
                
                if is_stream:
                    # 创建流式响应生成器
                    async def stream_generator():
                        content = parse_result.output
                        # 将内容分成多个chunk发送
                        chunk_size = max(1, len(content) // 5)  # 分成大约5个chunk
                        
                        for i in range(0, len(content), chunk_size):
                            chunk_content = content[i:i + chunk_size]
                            
                            chunk_data = {
                                "choices": [{
                                    "delta": {
                                        "content": chunk_content,
                                        "role": "assistant" if i == 0 else None
                                    },
                                    "index": 0
                                }],
                                "created": created_time,
                                "id": response_id,
                                "model": "image-parse-api",
                                "service_tier": "default",
                                "object": "chat.completion.chunk",
                                "usage": None
                            }
                            
                            # 如果是第一个chunk并且role为None，则移除role字段
                            if chunk_data["choices"][0]["delta"]["role"] is None:
                                del chunk_data["choices"][0]["delta"]["role"]
                            
                            yield f"data: {json.dumps(chunk_data, ensure_ascii=False)}"
                        
                        # 发送结束标记
                        yield "data: [DONE]"
                    
                    state["step_results"]["stream_generator"] = stream_generator()
                    logger.info("Stream generator created successfully")
                    
                else:
                    # 非流式响应
                    formatted_result = {
                        "choices": [{
                            "finish_reason": "stop",
                            "index": 0,
                            "logprobs": None,
                            "message": {
                                "content": parse_result.output,
                                "role": "assistant"
                            }
                        }],
                        "created": created_time,
                        "id": response_id,
                        "model": "image-parse-api",
                        "service_tier": "default",
                        "object": "chat.completion",
                        "usage": {
                            "completion_tokens": len(parse_result.output.split()),
                            "prompt_tokens": 10,  # 固定值，因为是图片解析
                            "total_tokens": len(parse_result.output.split()) + 10,
                            "prompt_tokens_details": {"cached_tokens": 0},
                            "completion_tokens_details": {"reasoning_tokens": 0}
                        }
                    }
                    state["step_results"]["formatted_result"] = formatted_result
                    logger.info("Non-stream result formatted successfully")
            else:
                # 解析失败时的错误响应
                error_result = {
                    "error": {
                        "message": parse_result.message,
                        "type": "image_parse_error",
                        "code": "parsing_failed"
                    }
                }
                
                if is_stream:
                    # 流式错误响应
                    async def error_stream_generator():
                        yield f"data: {json.dumps(error_result, ensure_ascii=False)}"
                        yield "data: [DONE]"
                    state["step_results"]["stream_generator"] = error_stream_generator()
                else:
                    state["step_results"]["formatted_result"] = error_result

        except Exception as e:
            state["error"] = f"Result formatting failed: {str(e)}"
            logger.error(f"Result formatting failed: {e}")

        return state

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Finalize and return formatted result"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]
            is_stream = step_results.get("is_stream", False)

            if is_stream:
                # 流式响应，返回生成器
                stream_generator = step_results.get("stream_generator")
                state["final_output"] = stream_generator
                logger.info("Image parsing workflow completed - stream mode")
            else:
                # 非流式响应，返回完整结果
                formatted_result = step_results.get("formatted_result", {})
                state["final_output"] = formatted_result
                logger.info("Image parsing workflow completed - non-stream mode")

        except Exception as e:
            state["error"] = f"Finalization failed: {str(e)}"
            logger.error(f"Finalization failed: {e}")

        return state

    # Direct access methods for API usage
    async def process_image(self, url: str, question: str = "描述这个图片", stream: bool = False) -> Union[Dict[str, Any], AsyncGenerator[str, None]]:
        """Image parsing with direct result passthrough"""
        
        # Prepare input data for LangGraph workflow
        input_data = {"url": url, "question": question, "stream": stream}

        # Execute the LangGraph workflow
        agent_state = await self.execute(input_data)
        
        if agent_state.status == TaskStatus.FAILED:
            # Return error in appropriate format
            error_response = {
                "error": {
                    "message": agent_state.error_message,
                    "type": "agent_error",
                    "code": "execution_failed"
                }
            }
            
            if stream:
                # 流式错误响应
                async def error_stream():
                    yield f"data: {json.dumps(error_response, ensure_ascii=False)}"
                    yield "data: [DONE]"
                return error_stream()
            else:
                return error_response

        # Return the result from workflow
        return agent_state.output_data
