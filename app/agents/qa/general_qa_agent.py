"""
General Q&A agent with LangGraph workflow - direct LLM passthrough
"""
from typing import Optional, AsyncGenerator, Union, Dict, Any, List
import json
import time

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.llm_service import llm_service
from app.services.web_search_service import web_search_service
from app.tools.search_tools import get_tool_schemas, execute_tool
from app.utils.logger import get_logger
from app.config import settings
from app.agents.content.context_utils import (
    estimate_messages_tokens,
    fit_messages_into_budget,
    truncate_messages_into_budget,
)
from app.agents.qa.prompts import (
    CHAT_SYSTEM_PROMPT,
    build_chat_messages
)

logger = get_logger(__name__)


class GeneralQAAgent(BaseAgent):
    """General Q&A agent with conversation history management and LLM passthrough"""

    def __init__(self, agent_type: AgentType):
        super().__init__(
            agent_type=agent_type,
            name="General Q&A Agent",
            description="Handles general Q&A with conversation history support and returns raw LLM responses"
        )

    def _build_graph(self):
        """Build LangGraph workflow for Q&A processing"""
        workflow = StateGraph(AgentWorkflowState)

        # Add processing nodes
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("truncate_history", self._truncate_history_node)
        workflow.add_node("preprocess_search", self._preprocess_search_node)
        workflow.add_node("check_tools_llm_call", self._check_tools_llm_call_node)  # 第一次调用：检查工具
        workflow.add_node("execute_tools", self._execute_tools_node)
        workflow.add_node("final_llm_call", self._final_llm_call_node)  # 第二次调用：生成最终响应
        workflow.add_node("finalize", self._finalize_node)

        # Define workflow edges
        workflow.set_entry_point("validate_input")
        # route based on input type
        workflow.add_conditional_edges(
            "validate_input",
            self._route_after_validation,
            {
                "truncate_history": "truncate_history",
            },
        )
        
        # 先进行预处理搜索判断，再进行第一次LLM调用（检查工具）
        workflow.add_edge("truncate_history", "preprocess_search")
        workflow.add_edge("preprocess_search", "check_tools_llm_call")
        
        # 第一次LLM调用后的路由：检查是否需要执行工具
        workflow.add_conditional_edges(
            "check_tools_llm_call",
            self._route_after_check_tools,
            {
                "execute_tools": "execute_tools",
                "final_llm_call": "final_llm_call",
                "finalize": "finalize"
            }
        )
        
        # 工具执行后的路由：检查是否需要流式输出
        workflow.add_conditional_edges(
            "execute_tools",
            self._route_after_tool_execution,
            {
                "final_llm_call": "final_llm_call",
                "finalize": "finalize"
            }
        )
        
        # 第二次LLM调用后直接完成
        workflow.add_edge("final_llm_call", "finalize")
        workflow.add_edge("finalize", END)

        # Compile the graph
        self.graph = workflow.compile()


    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Required by BaseAgent interface"""
        return state

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Validate and prepare input data for Q&A"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]

            messages = input_data.get("messages", [])
            model = input_data.get("model")
            is_stream = input_data.get("stream", False)
            max_tokens = input_data.get("max_tokens") or settings.default_max_tokens

            # Validate messages is not empty
            if not messages or len(messages) == 0:
                raise ValueError("messages must be a non-empty list")

            # Store validated data
            state["step_results"]["messages"] = messages
            state["step_results"]["model"] = model
            state["step_results"]["is_stream"] = is_stream
            state["step_results"]["max_tokens"] = max_tokens
            
            # Handle search options - 保存搜索配置供工具使用
            search_options = input_data.get("searchOptions")
            state["step_results"]["search_options"] = search_options



        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state






    async def _truncate_history_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Truncate history by tokens and ensure system prompt is present"""
        try:
            state["current_step"] = "truncate_history"
            step_results = state["step_results"]
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            model = step_results.get("model")
            incoming_messages = step_results.get("messages", [])
            search_options = step_results.get("search_options")

            # Check if system message exists
            has_system_message = incoming_messages and incoming_messages[0].get("role") == "system"
            
            # 添加系统提示词（不再包含搜索工具指导）
            if not has_system_message:
                # 只在没有system message时添加默认的
                incoming_messages = [{"role": "system", "content": CHAT_SYSTEM_PROMPT}] + incoming_messages
                logger.info("添加默认system prompt")

            context_window = settings.get_context_window(model)
            safety_margin = 300
            allowed_input_tokens = max(1000, context_window - max_tokens - safety_margin)

            processed_messages, token_count = truncate_messages_into_budget(
                incoming_messages, allowed_input_tokens
            )
            state["step_results"]["processed_messages"] = processed_messages
            state["step_results"]["prompt_tokens"] = token_count
            
            search_status = "启用搜索工具" if search_options and search_options.get("enableInternetSearch", False) else "普通对话模式"
            logger.info(
                f"Messages processing completed - {search_status}, {len(processed_messages)} messages, tokens≈{token_count}"
            )
        except Exception as e:
            state["error"] = f"Truncate history failed: {str(e)}"
            logger.error(f"Truncate history failed: {e}")
        return state

    def _route_after_validation(self, state: AgentWorkflowState) -> str:
        """Route based on input type - simplified for tool-based approach"""
        # All inputs are now messages, so we always use truncate_history
        return "truncate_history"

    async def _preprocess_search_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """预处理搜索：根据参数直接决定是否需要搜索，跳过智能判断"""
        try:
            state["current_step"] = "preprocess_search"
            step_results = state["step_results"]
            
            search_options = step_results.get("search_options")
            
            # 检查是否启用搜索功能
            if not search_options:
                logger.info("未提供搜索选项，跳过预处理搜索")
                step_results["has_search_results"] = False
                step_results["need_search"] = False
                return state
            
            enable_internet_search = search_options.get("enableInternetSearch", False)
            provider = search_options.get("provider", "hiagent")
            
            # 将provider标准化为列表
            if isinstance(provider, str):
                provider_list = [provider]
            else:
                provider_list = provider if provider else ["hiagent"]
            
            # 根据参数决定是否需要搜索
            need_search = False
            search_providers = []
            
            # 如果启用联网搜索，一定调用hiagent
            if enable_internet_search:
                search_providers.append("hiagent")
                need_search = True
                logger.info("启用联网搜索，将调用hiagent搜索")
            
            # 根据provider参数决定是否调用wiki和huatech
            for prov in provider_list:
                if prov in ["wiki", "huatech"] and prov not in search_providers:
                    search_providers.append(prov)
                    need_search = True
                    logger.info(f"根据provider参数，将调用{prov}搜索")
            
            if need_search:
                logger.info(f"需要执行搜索，providers: {search_providers}")
                step_results["need_search"] = True
                step_results["search_providers"] = search_providers
                step_results["has_search_results"] = False  # 还未执行搜索
                step_results["single_llm_call"] = False  # 需要两次LLM调用
            else:
                logger.info("不需要执行任何搜索，使用单次LLM调用")
                step_results["need_search"] = False
                step_results["has_search_results"] = False
                step_results["single_llm_call"] = True  # 只需要一次LLM调用
            
        except Exception as e:
            logger.error(f"预处理搜索失败: {e}", exc_info=True)
            # 搜索失败不影响正常对话，继续执行
            step_results["need_search"] = False
            step_results["has_search_results"] = False
            step_results["single_llm_call"] = True  # 失败时回退到单次调用
            
        return state


    async def _check_tools_llm_call_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """第一次LLM调用：如果需要搜索则生成搜索查询词，否则直接回答"""
        try:
            state["current_step"] = "check_tools_llm_call"
            step_results = state["step_results"]
            
            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")
            # 保存用户要求的流式模式设置
            user_requested_stream = step_results.get("is_stream", False)
            step_results["user_requested_stream"] = user_requested_stream
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            
            need_search = step_results.get("need_search", False)
            single_llm_call = step_results.get("single_llm_call", False)
            search_providers = step_results.get("search_providers", [])
            
            if single_llm_call:
                # 不需要搜索，直接进行完整的LLM调用回答问题
                logger.info("单次LLM调用模式，直接回答用户问题")
                
                llm_response = await llm_service.chat_completion(
                    messages=messages,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                
                # 保存响应用于直接输出
                state["step_results"]["first_llm_response"] = llm_response
                state["step_results"]["direct_response"] = llm_response
                step_results["ready_to_search"] = False
                
            elif need_search:
                # 需要搜索，第一次LLM调用用于生成搜索查询词
                logger.info(f"两阶段调用模式，第一次LLM调用生成搜索查询词，providers: {search_providers}")
                
                # 构建生成搜索查询词的prompt
                search_query_prompt = f"""请根据用户的问题生成1-3个搜索关键词，用于搜索相关信息。

要求：
1. 关键词要简洁、准确，能够找到相关信息
2. 多个关键词之间用空格分隔
3. 只返回关键词，不要其他内容

用户问题：{messages[-1].get('content', '') if messages else ''}

搜索关键词："""

                search_messages = [
                    {"role": "system", "content": "你是一个搜索关键词生成助手，根据用户问题生成准确的搜索关键词。"},
                    {"role": "user", "content": search_query_prompt}
                ]
                
                llm_response = await llm_service.chat_completion(
                    messages=search_messages,
                    model=model,
                    max_tokens=100,  # 搜索关键词不需要太多token
                    temperature=0.3  # 降低温度确保关键词准确
                )
                
                # 提取搜索查询词
                if llm_response and "choices" in llm_response and llm_response["choices"]:
                    search_query = llm_response["choices"][0]["message"]["content"].strip()
                    logger.info(f"生成的搜索查询词: '{search_query}'")
                    step_results["search_query"] = search_query
                else:
                    # 如果生成失败，使用用户问题作为搜索词
                    search_query = messages[-1].get('content', '') if messages else ''
                    logger.warning(f"搜索词生成失败，使用用户问题: '{search_query}'")
                    step_results["search_query"] = search_query
                
                # 标记需要执行搜索
                step_results["ready_to_search"] = True
                state["step_results"]["first_llm_response"] = llm_response
                
            else:
                # 这种情况理论上不应该发生，但作为保险
                logger.warning("未知的调用模式，回退到直接回答")
                
                llm_response = await llm_service.chat_completion(
                    messages=messages,
                    model=model,
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                
                # 保存响应用于直接输出
                state["step_results"]["first_llm_response"] = llm_response
                state["step_results"]["direct_response"] = llm_response
                step_results["ready_to_search"] = False
            
        except Exception as e:
            state["error"] = f"LLM call failed: {str(e)}"
            logger.error(f"LLM call failed: {e}")
            
        return state

    def _route_after_check_tools(self, state: AgentWorkflowState) -> str:
        """第一次LLM调用后的路由：决定是否需要搜索"""
        step_results = state.get("step_results", {})
        
        # 检查是否是单次LLM调用模式
        single_llm_call = step_results.get("single_llm_call", False)
        if single_llm_call:
            # 单次调用模式，直接结束
            logger.info("单次LLM调用模式，直接返回结果")
            return "finalize"
        
        # 检查是否准备好搜索
        if step_results.get("ready_to_search"):
            return "execute_tools"  # 复用execute_tools节点来执行搜索
        else:
            # 不需要搜索，检查用户是否要求流式输出
            user_requested_stream = step_results.get("user_requested_stream", False)
            if user_requested_stream:
                return "final_llm_call"
            else:
                return "finalize"

    def _route_after_tool_execution(self, state: AgentWorkflowState) -> str:
        """搜索执行后的路由：总是进行第二次LLM调用"""
        step_results = state.get("step_results", {})
        
        # 搜索完成后，总是进行第二次LLM调用来基于搜索结果生成回答
        # 检查用户是否要求流式输出
        user_requested_stream = step_results.get("user_requested_stream", False)
        if user_requested_stream:
            return "final_llm_call"  # 流式第二次调用
        else:
            # 非流式第二次调用，直接在finalize中处理
            return "finalize"

    async def _execute_tools_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """执行搜索操作"""
        try:
            state["current_step"] = "execute_search"
            step_results = state["step_results"]
            
            search_query = step_results.get("search_query", "")
            search_providers = step_results.get("search_providers", [])
            search_options = step_results.get("search_options", {})
            messages = step_results.get("processed_messages", [])
            
            logger.info(f"⚡ 开始执行搜索: query='{search_query}', providers={search_providers}")
            
            all_search_results = []
            
            # 对每个provider执行搜索
            for provider in search_providers:
                try:
                    logger.info(f"🔍 执行{provider}搜索")
                    search_results = await web_search_service.search(
                        query=search_query,
                        provider=provider,
                        snippet=False,
                        authInfos=search_options.get("authInfos")
                    )
                    
                    if search_results:
                        all_search_results.extend(search_results)
                        logger.info(f"✅ {provider}搜索成功，获得 {len(search_results)} 条结果")
                    else:
                        logger.warning(f"⚠️ {provider}搜索未返回结果")
                        
                except Exception as e:
                    logger.error(f"❌ {provider}搜索失败: {e}")
            
            if all_search_results:
                # 格式化搜索结果并添加到消息历史
                search_context = web_search_service.format_search_results(all_search_results, max_results=5)
                logger.info(f"格式化搜索结果，总共 {len(all_search_results)} 条，长度: {len(search_context)} 字符")
                
                # 将搜索结果作为系统消息加入到对话中
                search_message = {
                    "role": "system", 
                    "content": f"**搜索结果**（请基于以下信息回答用户问题）：\n\n{search_context}"
                }
                
                # 添加搜索结果到消息历史
                messages.append(search_message)
                step_results["processed_messages"] = messages
                step_results["has_search_results"] = True
                
                # 保存搜索结果供最终引用使用
                step_results["search_results"] = all_search_results
                
                logger.info(f"成功将搜索结果加入context，搜索结果预览: {search_context[:200]}...")
            else:
                logger.warning("所有搜索都未返回结果")
                step_results["has_search_results"] = False
            
            # 清除搜索状态
            step_results["ready_to_search"] = False
            
        except Exception as e:
            state["error"] = f"搜索执行失败: {str(e)}"
            logger.error(f"搜索执行失败: {e}")
            # 搜索失败不影响正常对话
            step_results["has_search_results"] = False
            step_results["ready_to_search"] = False
            
        return state

    async def _final_llm_call_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """第二次LLM调用：始终使用流式模式生成最终响应"""
        try:
            state["current_step"] = "final_llm_call"
            step_results = state["step_results"]
            
            # 获取处理后的消息（可能包含工具结果）
            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            
            
            # 第二次调用：始终使用流式模式（不需要工具调用）
            stream_generator = llm_service.stream_chat_completion(
                messages=messages,
                model=model,
                max_tokens=max_tokens,
                temperature=0.7,
                tools=None  # 第二次调用不需要工具
            )
            
            # 保存流式生成器
            state["step_results"]["llm_stream"] = stream_generator
            
        except Exception as e:
            state["error"] = f"Final LLM call failed: {str(e)}"
            logger.error(f"Final LLM call failed: {e}")
            
        return state

    async def _perform_final_non_stream_call(self, state: AgentWorkflowState):
        """执行工具后的非流式最终LLM调用"""
        try:
            step_results = state["step_results"]
            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)
            search_options = step_results.get("search_options")
            
            # 使用非流式调用
            final_response = await llm_service.chat_completion(
                messages=messages,
                model=model,
                max_tokens=max_tokens,
                temperature=0.7,
                tools=None  # 最终调用不需要工具
            )
            
            # 如果启用了搜索且有搜索结果，在响应后添加引用
            if (search_options and search_options.get("enableInternetSearch", False) 
                and step_results.get("search_results")):
                final_response = self._add_search_references_to_response(final_response, step_results)
            
            state["final_output"] = final_response
            
        except Exception as e:
            logger.error(f"非流式最终调用失败: {e}", exc_info=True)
            # 回退到第一次响应
            state["final_output"] = step_results.get("first_llm_response", {})

    async def _call_llm_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Call LLM and store response (streaming or non-streaming)"""
        try:
            state["current_step"] = "call_llm"
            step_results = state["step_results"]

            messages = step_results.get("processed_messages", [])
            model = step_results.get("model")  # Get custom model
            is_stream = step_results.get("is_stream", False)
            max_tokens = step_results.get("max_tokens", settings.default_max_tokens)

            if is_stream:
                # For streaming: create and store the stream generator
                stream_generator = llm_service.stream_chat_completion(
                    messages=messages,
                    model=model,  # Pass custom model
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                state["step_results"]["llm_stream"] = stream_generator
            else:
                # For non-streaming: get the complete response
                llm_response = await llm_service.chat_completion(
                    messages=messages,
                    model=model,  # Pass custom model
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                state["step_results"]["llm_response"] = llm_response

        except Exception as e:
            state["error"] = f"LLM call failed: {str(e)}"
            logger.error(f"LLM call failed: {e}")

        return state

    def _add_search_references_to_response(self, response: dict, step_results: dict) -> dict:
        """为非流式响应添加搜索结果引用"""
        try:
            search_results = step_results.get("search_results", [])
            if not search_results:
                return response
            
            # 导入搜索服务以使用格式化方法
            from app.services.web_search_service import SearchResult, web_search_service
            
            # 转换为SearchResult对象
            search_result_objects = []
            for result in search_results:
                if isinstance(result, dict):
                    search_result_objects.append(SearchResult(
                        title=result.get("title", ""),
                        content=result.get("content", ""),
                        url=result.get("url", ""),
                        source=result.get("source", "")
                    ))
                elif hasattr(result, 'title'):  # 已经是SearchResult对象
                    search_result_objects.append(result)
            
            # 生成简单格式的引用
            references = web_search_service.format_simple_references(search_result_objects)
            
            if references and "choices" in response and response["choices"]:
                # 在响应内容后添加引用
                message = response["choices"][0].get("message", {})
                content = message.get("content", "")
                enhanced_content = f"{content}\n\n{references}"
                
                # 创建新的响应对象
                enhanced_response = response.copy()
                enhanced_response["choices"] = response["choices"].copy()
                enhanced_response["choices"][0] = response["choices"][0].copy()
                enhanced_response["choices"][0]["message"] = message.copy()
                enhanced_response["choices"][0]["message"]["content"] = enhanced_content
                
                return enhanced_response
                
        except Exception as e:
            logger.error(f"添加搜索引用失败: {e}", exc_info=True)
        
        return response

    async def _create_stream_with_references(self, original_stream, step_results: dict):
        """创建带搜索引用的流式响应包装器"""
        try:
            search_results = step_results.get("search_results", [])
            search_options = step_results.get("search_options")
            
            # 先输出原始流
            async for chunk in original_stream:
                yield chunk
                
            # 如果启用了搜索且有搜索结果，在流结束时添加引用
            if (search_options and search_options.get("enableInternetSearch", False) 
                and search_results):
                
                # 导入搜索服务
                from app.services.web_search_service import SearchResult, web_search_service
                
                # 转换为SearchResult对象
                search_result_objects = []
                for result in search_results:
                    if isinstance(result, dict):
                        search_result_objects.append(SearchResult(
                            title=result.get("title", ""),
                            content=result.get("content", ""),
                            url=result.get("url", ""),
                            source=result.get("source", "")
                        ))
                    elif hasattr(result, 'title'):  # 已经是SearchResult对象
                        search_result_objects.append(result)
                
                # 生成简单格式的引用
                references = web_search_service.format_simple_references(search_result_objects)
                
                if references:
                    # 构造引用的流式chunk
                    reference_chunk = {
                        "id": "search_references",
                        "object": "chat.completion.chunk", 
                        "created": int(time.time()),
                        "model": step_results.get("model", "unknown"),
                        "choices": [{
                            "index": 0,
                            "delta": {
                                "content": f"\n\n{references}"
                            },
                            "finish_reason": None
                        }]
                    }
                    yield f"data: {json.dumps(reference_chunk, ensure_ascii=False)}\n\n"
                    
                    # 发送结束标志
                    end_chunk = {
                        "id": "search_references", 
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": step_results.get("model", "unknown"),
                        "choices": [{
                            "index": 0,
                            "delta": {},
                            "finish_reason": "stop"
                        }]
                    }
                    yield f"data: {json.dumps(end_chunk, ensure_ascii=False)}\n\n"
                    yield "data: [DONE]\n\n"
                    
        except Exception as e:
            logger.error(f"流式搜索引用处理失败: {e}", exc_info=True)
            # 如果处理失败，继续输出原始流
            async for chunk in original_stream:
                yield chunk

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Finalize and return appropriate response based on workflow path"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]

            user_requested_stream = step_results.get("user_requested_stream", False)
            tool_results = step_results.get("tool_results", [])
            single_llm_call = step_results.get("single_llm_call", False)

            search_options = step_results.get("search_options")
            has_search_enabled = search_options and search_options.get("enableInternetSearch", False)
            has_search_results = bool(step_results.get("search_results"))

            # 根据流程决定返回什么
            if single_llm_call:
                # 单次LLM调用模式，直接返回第一次调用的结果
                logger.info("单次LLM调用模式，返回直接响应")
                direct_response = step_results.get("direct_response", step_results.get("first_llm_response", {}))
                state["final_output"] = direct_response
            elif step_results.get("llm_stream"):
                # 有流式生成器（来自第二次LLM调用）
                original_stream = step_results.get("llm_stream")
                if has_search_enabled and has_search_results:
                    # 如果启用了搜索且有搜索结果，包装流式响应
                    state["final_output"] = self._create_stream_with_references(original_stream, step_results)
                else:
                    state["final_output"] = original_stream
            elif has_search_results and not user_requested_stream:
                # 有搜索结果但用户不要求流式，需要进行非流式的第二次LLM调用
                logger.info("搜索执行完成，进行非流式第二次LLM调用")
                await self._perform_final_non_stream_call(state)
            elif step_results.get("direct_response"):
                # 没有搜索，用户不要求流式，直接返回第一次的响应
                direct_response = step_results.get("direct_response", {})
                if has_search_enabled and has_search_results:
                    # 如果启用了搜索且有搜索结果，添加引用
                    direct_response = self._add_search_references_to_response(direct_response, step_results)
                state["final_output"] = direct_response
            elif step_results.get("first_llm_response"):
                # 回退到第一次响应
                first_response = step_results.get("first_llm_response", {})
                if has_search_enabled and has_search_results:
                    # 如果启用了搜索且有搜索结果，添加引用
                    first_response = self._add_search_references_to_response(first_response, step_results)
                state["final_output"] = first_response
            else:
                # 完全回退
                logger.error("没有找到任何有效响应")
                fallback_response = step_results.get("llm_response", {})
                if has_search_enabled and has_search_results:
                    fallback_response = self._add_search_references_to_response(fallback_response, step_results)
                state["final_output"] = fallback_response

        except Exception as e:
            state["error"] = f"Finalization failed: {str(e)}"
            logger.error(f"Finalization failed: {e}", exc_info=True)

        return state



    # Direct access methods for API usage
    async def process_chat(self,
                           messages: List[Dict[str, str]],
                           model: Optional[str] = None,
                           stream: bool = False,
                           max_tokens: Optional[int] = None,
                           searchOptions: Optional[Dict[str, Any]] = None) -> Union[Any, AsyncGenerator[str, None]]:
        """Q&A processing with smart conversation history management"""
        
        # Prepare input data for LangGraph workflow
        input_data = {
            "messages": messages,
            "model": model,
            "stream": stream,
            "max_tokens": max_tokens or settings.default_max_tokens,
            "searchOptions": searchOptions,
        }

        # Execute the LangGraph workflow
        agent_state = await self.execute(input_data)
        
        if agent_state.status == TaskStatus.FAILED:
            # Return error in appropriate format
            error_response = {"error": agent_state.error_message}
            if stream:
                async def error_stream():
                    yield f"data: {json.dumps(error_response)}"
                return error_stream()
            else:
                return error_response

        # Return the result from workflow (either stream generator or response data)
        return agent_state.output_data
