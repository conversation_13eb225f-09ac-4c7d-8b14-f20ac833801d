"""
Prompt Optimizer Agent with LangGraph workflow - 提示词优化智能体
"""
from typing import Optional, AsyncGenerator, Union, Dict, Any, List
import json

from langgraph.graph import StateGraph, END

from app.agents.base import BaseAgent, AgentWorkflowState
from app.core.models import AgentType, TaskStatus
from app.services.llm_service import llm_service
from app.utils.logger import get_logger
from app.config import settings
from .prompts import PROMPT_OPTIMIZER_SYSTEM_PROMPT, PROMPT_OPTIMIZER_USER_TEMPLATE

logger = get_logger(__name__)


class PromptOptimizerAgent(BaseAgent):
    """提示词优化智能体"""

    def __init__(self, agent_type: AgentType):
        super().__init__(
            agent_type=agent_type,
            name="Prompt Optimizer Agent",
            description="专业的提示词优化智能体，提供提示词改进服务"
        )

    def _build_graph(self):
        """构建 LangGraph 工作流"""
        workflow = StateGraph(AgentWorkflowState)

        # 添加处理节点
        workflow.add_node("validate_input", self._validate_input_node)
        workflow.add_node("optimize_prompt", self._optimize_prompt_node)
        workflow.add_node("finalize", self._finalize_node)

        # 定义工作流边
        workflow.set_entry_point("validate_input")
        workflow.add_edge("validate_input", "optimize_prompt")
        workflow.add_edge("optimize_prompt", "finalize")
        workflow.add_edge("finalize", END)

        # 编译图
        self.graph = workflow.compile()
        logger.info("LangGraph workflow built for prompt optimizer agent")

    async def _validate_input_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """验证输入参数"""
        try:
            state["current_step"] = "validate_input"
            input_data = state["input_data"]

            # 验证必需字段
            if "question" not in input_data or not input_data["question"]:
                raise ValueError("question field is required and cannot be empty")

            question = input_data["question"]
            if not isinstance(question, str) or not question.strip():
                raise ValueError("question must be a non-empty string")

            # 存储验证后的输入
            state["step_results"]["validated_input"] = {
                "question": question.strip(),
                "stream": input_data.get("stream", False),
                "model": input_data.get("model")
            }

            logger.info(f"输入验证完成: question length={len(question)}, stream={input_data.get('stream', False)}")

        except Exception as e:
            state["error"] = f"Input validation failed: {str(e)}"
            logger.error(f"Input validation failed: {e}")

        return state

    async def _optimize_prompt_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """执行提示词优化"""
        try:
            state["current_step"] = "optimize_prompt"
            step_results = state["step_results"]
            validated_input = step_results["validated_input"]

            original_prompt = validated_input["question"]
            is_stream = validated_input["stream"]
            model = validated_input.get("model")

            # 构建优化请求的消息
            messages = [
                {"role": "system", "content": PROMPT_OPTIMIZER_SYSTEM_PROMPT},
                {"role": "user", "content": PROMPT_OPTIMIZER_USER_TEMPLATE.format(original_prompt=original_prompt)}
            ]

            logger.info(f"开始提示词优化: model={model}, stream={is_stream}")

            if is_stream:
                # 流式模式
                stream_generator = llm_service.stream_chat_completion(
                    messages=messages,
                    model=model,
                    max_tokens=2000,
                    temperature=0.7
                )
                state["step_results"]["optimization_stream"] = stream_generator
                state["step_results"]["is_streaming"] = True
                logger.info("流式提示词优化已准备就绪")
            else:
                # 非流式模式
                result = await llm_service.chat_completion(
                    messages=messages,
                    model=model,
                    max_tokens=2000,
                    temperature=0.7
                )
                state["step_results"]["optimization_result"] = result
                logger.info("提示词优化完成")

        except Exception as e:
            state["error"] = f"Prompt optimization failed: {str(e)}"
            logger.error(f"Prompt optimization failed: {e}")

        return state

    async def _finalize_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """完成提示词优化结果"""
        try:
            state["current_step"] = "finalize"
            step_results = state["step_results"]

            # 检查是否为流式优化
            if step_results.get("is_streaming"):
                # 流式模式：将stream generator存储到final_output中
                stream_generator = step_results.get("optimization_stream")
                if stream_generator:
                    state["final_output"] = stream_generator
                    logger.info("流式提示词优化流程完成，stream generator已准备就绪")
                else:
                    state["final_output"] = {
                        "choices": [{
                            "finish_reason": "stop",
                            "index": 0,
                            "logprobs": None,
                            "message": {
                                "content": "Stream generator not found",
                                "role": "assistant"
                            }
                        }],
                        "created": int(__import__('time').time()),
                        "id": f"prompt-optimizer-{__import__('uuid').uuid4().hex[:8]}",
                        "model": step_results["validated_input"].get("model", settings.default_llm_model),
                        "object": "chat.completion",
                        "usage": {
                            "completion_tokens": 0,
                            "prompt_tokens": 0,
                            "total_tokens": 0
                        }
                    }
                    logger.error("流式提示词优化失败：未找到stream generator")
            else:
                # 非流式模式：直接返回LLM响应
                result = step_results.get("optimization_result")
                if result:
                    state["final_output"] = result
                else:
                    # 构造错误响应
                    state["final_output"] = {
                        "choices": [{
                            "finish_reason": "stop",
                            "index": 0,
                            "logprobs": None,
                            "message": {
                                "content": "提示词优化失败，请稍后重试。",
                                "role": "assistant"
                            }
                        }],
                        "created": int(__import__('time').time()),
                        "id": f"prompt-optimizer-{__import__('uuid').uuid4().hex[:8]}",
                        "model": step_results["validated_input"].get("model", settings.default_llm_model),
                        "object": "chat.completion",
                        "usage": {
                            "completion_tokens": 0,
                            "prompt_tokens": 0,
                            "total_tokens": 0
                        }
                    }

                logger.info("非流式提示词优化流程完成")

        except Exception as e:
            state["error"] = f"Finalization failed: {str(e)}"
            logger.error(f"Finalization failed: {e}")

        return state

    # Required abstract method implementation
    async def process_node(self, state: AgentWorkflowState) -> AgentWorkflowState:
        """Process node logic (required by base class)"""
        return state

    # Direct access methods for API usage
    async def process_optimization(self,
                                 question: str,
                                 stream: bool = False,
                                 model: Optional[str] = None) -> Union[Dict[str, Any], AsyncGenerator[str, None]]:
        """提示词优化处理，直接返回结果"""

        # 准备LangGraph工作流的输入数据
        input_data = {
            "question": question,
            "stream": stream,
            "model": model
        }

        # 执行LangGraph工作流
        agent_state = await self.execute(input_data)

        if agent_state.status == TaskStatus.FAILED:
            # 返回错误格式
            error_response = {
                "choices": [{
                    "finish_reason": "stop",
                    "index": 0,
                    "logprobs": None,
                    "message": {
                        "content": agent_state.error_message or "提示词优化失败",
                        "role": "assistant"
                    }
                }],
                "created": int(__import__('time').time()),
                "id": f"prompt-optimizer-error-{__import__('uuid').uuid4().hex[:8]}",
                "model": model or settings.default_llm_model,
                "object": "chat.completion",
                "usage": {
                    "completion_tokens": 0,
                    "prompt_tokens": 0,
                    "total_tokens": 0
                }
            }

            if stream:
                # 流式错误响应
                async def error_stream():
                    yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                    yield "data: [DONE]\n\n"
                return error_stream()
            else:
                return error_response

        # 返回成功结果
        output_data = agent_state.output_data

        # 检查是否为流式优化且有stream generator
        if stream and hasattr(output_data, '__aiter__'):
            # 流式优化：直接返回stream generator
            logger.info("返回流式提示词优化生成器")
            return output_data
        elif stream:
            # 非流式结果的流式包装
            logger.info("返回非流式结果的流式包装")
            async def success_stream():
                yield f"data: {json.dumps(output_data, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
            return success_stream()
        else:
            # 非流式返回
            logger.info("返回非流式提示词优化结果")
            return output_data
