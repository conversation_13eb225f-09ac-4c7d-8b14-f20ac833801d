"""
通用问答API - LangGraph agent with direct LLM passthrough
"""
from fastapi import APIRouter
from fastapi.responses import JSO<PERSON>esponse, StreamingResponse

from app.agents.base import agent_registry
from app.api.models import ChatRequest
from app.core.models import AgentType
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/default", 
             summary="General Q&A",
             description="Answer questions or continue conversations with smart context management; returns raw LLM API format.")
async def default_chat(request: ChatRequest):
    """General Q&A with LangGraph processing and raw LLM passthrough"""
    
    try:
        # Create Q&A agent
        agent = agent_registry.create_agent(AgentType.GENERAL_QA)

        # Use agent's direct processing method
        search_options = None
        if request.searchOptions:
            search_options = request.searchOptions.model_dump() if hasattr(request.searchOptions, 'model_dump') else request.searchOptions
        
        result = await agent.process_chat(
            messages=request.messages,
            model=request.model,
            stream=request.stream,
            max_tokens=None,
            searchOptions=search_options
        )

        if request.stream:
            # Return raw LLM stream
            async def stream_generator():
                try:
                    async for line in result:
                        yield f"{line}\n\n"
                except Exception as e:
                    logger.error(f"流式生成异常: {e}", exc_info=True)
            
            return StreamingResponse(
                stream_generator(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "*"
                }
            )
        else:
            return JSONResponse(status_code=200, content=result)
            
    except Exception as e:
        logger.error(f"API调用失败: {e}")
        import traceback
        logger.error(f"详细错误堆栈: {traceback.format_exc()}")
        raise
