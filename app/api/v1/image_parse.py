"""
图片解析API - LangGraph agent with direct API passthrough
"""
from fastapi import APIRouter
from fastapi.responses import JSONResponse, StreamingResponse

from app.agents.base import agent_registry
from app.api.models import ImageParseRequest
from app.core.models import AgentType
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/image-parse", 
             summary="Image Parse",
             description="Parse image content from URL and return extracted text; supports streaming and non-streaming modes.")
async def parse_image(request: ImageParseRequest):
    """Image parsing with LangGraph processing and direct result passthrough"""
    logger.info(f"Processing image parse request for URL: {request.url}, question: {request.question}, stream: {request.stream}")

    # Create image parse agent
    agent = agent_registry.create_agent(AgentType.IMAGE_PARSE)
    
    # Use agent's direct processing method
    result = await agent.process_image(url=request.url, question=request.question, stream=request.stream)

    if request.stream:
        # Return streaming response
        async def stream_generator():
            async for line in result:
                yield f"{line}\n\n"
        
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )
    else:
        # Return non-streaming JSON response
        return JSONResponse(status_code=200, content=result)
