"""
联网搜索服务
"""
import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass

from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SearchRequest:
    """搜索请求数据结构"""
    query: str
    provider: Union[str, List[str]] = "hiagent"
    snippet: bool = False
    authInfos: Optional[Dict[str, Dict[str, Any]]] = None


@dataclass 
class SearchResult:
    """搜索结果数据结构"""
    title: str
    content: str
    url: str
    source: str


class WebSearchClient:
    """联网搜索客户端"""

    def __init__(self, api_url: str = "http://*************/fst/api/v1/websearch"):
        self.api_url = api_url
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self._timeout)
        return self.session

    def _build_payload(self, request: SearchRequest) -> Dict[str, Any]:
        """构建搜索请求载荷"""
        payload = {
            "query": request.query,
            "provider": request.provider,
            "snippet": request.snippet
        }
        
        if request.authInfos:
            payload["authInfos"] = request.authInfos
            
        return payload

    async def search(self, request: SearchRequest) -> List[SearchResult]:
        """执行搜索并返回结构化结果"""
        session = await self._get_session()
        payload = self._build_payload(request)
        
        # 详细调试日志
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Apipost client Runtime/+https://www.apipost.cn/"
        }
        
        try:
            
            async with session.post(
                self.api_url,
                headers=headers,
                json=payload
            ) as response:
                response_text = await response.text()
                
                if response.status == 200:
                    try:
                        result = json.loads(response_text)
                        parsed_results = self._parse_search_results(result, request.provider)
                        return parsed_results
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {str(e)}")
                        logger.error(f"原始响应: {response_text}")
                        return []
                else:
                    logger.error(f"搜索请求失败: {response.status}")
                    logger.error(f"错误内容: {response_text}")
                    return []
                    
        except Exception as e:
            logger.error(f"搜索执行异常: {str(e)}", exc_info=True)
            return []

    def _parse_search_results(self, response: Dict[str, Any], provider: Union[str, List[str]]) -> List[SearchResult]:
        """解析搜索API响应为标准格式"""
        results = []
        
        try:
            # 根据实际API响应格式解析
            if "code" in response:
                if response["code"] == "0":
                    # 检查data字段
                    data = response.get("data", {})
                    
                    if isinstance(data, dict) and "results" in data:
                        # data.results是结果列表
                        search_results = data["results"]
                        
                        if isinstance(search_results, list):
                            for i, item in enumerate(search_results):
                                result = SearchResult(
                                    title=item.get("title", ""),
                                    content=item.get("content", ""),
                                    url=item.get("url", ""),
                                    source=item.get("providerName", item.get("provider", str(provider)))
                                )
                                results.append(result)
                    elif isinstance(data, list):
                        # data直接是结果列表（向后兼容）
                        for i, item in enumerate(data):
                            result = SearchResult(
                                title=item.get("title", ""),
                                content=item.get("content", ""),
                                url=item.get("url", ""),
                                source=item.get("providerName", item.get("provider", str(provider)))
                            )
                            results.append(result)
                    else:
                        logger.warning(f"未识别的data格式: {type(data)}, 内容: {data}")
                else:
                    logger.warning(f"响应code不是'0': {response['code']}")
                    logger.warning(f"完整错误响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
            else:
                logger.warning("响应中没有'code'字段")
                logger.warning(f"完整响应: {json.dumps(response, ensure_ascii=False, indent=2)}")
                        
            
        except Exception as e:
            logger.error(f"解析搜索结果失败: {str(e)}", exc_info=True)
            logger.error(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
            
        return results

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None


class WebSearchService:
    """联网搜索服务管理器"""

    def __init__(self):
        # 使用配置中的搜索API URL，如果没有设置则使用默认值
        search_api_url = getattr(settings, 'web_search_api_url', 'http://*************/fst/api/v1/websearch')
        self.client = WebSearchClient(api_url=search_api_url)

    async def search(self, 
                    query: str,
                    provider: Union[str, List[str]] = "hiagent",
                    snippet: bool = False,
                    authInfos: Optional[Dict[str, Dict[str, Any]]] = None) -> List[SearchResult]:
        """执行联网搜索"""
        request = SearchRequest(
            query=query,
            provider=provider,
            snippet=snippet,
            authInfos=authInfos
        )
        
        results = await self.client.search(request)
        return results

    def format_search_results(self, results: List[SearchResult], max_results: int = 5) -> str:
        """格式化搜索结果为文本，按provider分类"""
        if not results:
            return "未找到相关搜索结果。"
        
        # 按provider分类
        wiki_results = []
        huatech_results = []
        web_results = []
        
        for result in results[:max_results]:
            if "wiki" in result.source.lower():
                wiki_results.append(result)
            elif "huatech" in result.source.lower():
                huatech_results.append(result)
            else:
                web_results.append(result)
        
        formatted_sections = []
        
        # 联网搜索结果
        if web_results:
            formatted_sections.append("=== 🌐 联网搜索结果 ===")
            for i, result in enumerate(web_results, 1):
                formatted_result = f"""
【联网搜索 {i}】
标题: {result.title}
来源: 网络搜索
链接: {result.url}
内容: {result.content[:500]}{"..." if len(result.content) > 500 else ""}
"""
                formatted_sections.append(formatted_result.strip())
        
        # Wiki搜索结果
        if wiki_results:
            formatted_sections.append("\n=== 📚 Wiki搜索结果 ===")
            for i, result in enumerate(wiki_results, 1):
                formatted_result = f"""
【Wiki搜索 {i}】
标题: {result.title}
来源: {result.source}
链接: {result.url}
内容: {result.content[:500]}{"..." if len(result.content) > 500 else ""}
"""
                formatted_sections.append(formatted_result.strip())
        
        # Huatech搜索结果
        if huatech_results:
            formatted_sections.append("\n=== 🏢 Huatech搜索结果 ===")
            for i, result in enumerate(huatech_results, 1):
                formatted_result = f"""
【Huatech搜索 {i}】
标题: {result.title}
来源: {result.source}
链接: {result.url}
内容: {result.content[:500]}{"..." if len(result.content) > 500 else ""}
"""
                formatted_sections.append(formatted_result.strip())
        
        return "\n\n".join(formatted_sections)

    def format_reference_links(self, results: List[SearchResult], max_results: int = 5) -> str:
        """格式化参考链接，按provider分类"""
        if not results:
            return ""
        
        # 按provider分类
        wiki_results = []
        huatech_results = []
        web_results = []
        
        for result in results[:max_results]:
            if "wiki" in result.source.lower():
                wiki_results.append(result)
            elif "huatech" in result.source.lower():
                huatech_results.append(result)
            else:
                web_results.append(result)
        
        links_sections = []
        
        # 联网搜索链接
        if web_results:
            links_sections.append("### 🌐 联网搜索")
            for i, result in enumerate(web_results, 1):
                links_sections.append(f"{i}. [{result.title}]({result.url})")
        
        # Wiki搜索链接
        if wiki_results:
            links_sections.append("\n### 📚 Wiki搜索")
            for i, result in enumerate(wiki_results, 1):
                links_sections.append(f"{i}. [{result.title}]({result.url})")
        
        # Huatech搜索链接
        if huatech_results:
            links_sections.append("\n### 🏢 Huatech搜索")
            for i, result in enumerate(huatech_results, 1):
                links_sections.append(f"{i}. [{result.title}]({result.url})")
        
        if links_sections:
            return "\n\n## 📚 参考链接\n\n" + "\n".join(links_sections)
        return ""

    def format_simple_references(self, results: List[SearchResult], max_results: int = 5) -> str:
        """格式化简单的搜索结果引用，按provider分类"""
        if not results:
            return ""
        
        # 按provider分类
        wiki_results = []
        huatech_results = []
        web_results = []
        
        for result in results[:max_results]:
            if "wiki" in result.source.lower():
                wiki_results.append(result)
            elif "huatech" in result.source.lower():
                huatech_results.append(result)
            else:
                web_results.append(result)
        
        references = []
        
        # 外网搜索
        if web_results:
            references.append("外网搜索：")
            for result in web_results:
                references.append(f"{result.title} {result.url}")
        
        # wiki搜索
        if wiki_results:
            if references:  # 如果前面有搜索结果，需要空行分隔
                references.append("")
            references.append("wiki搜索：")
            for result in wiki_results:
                references.append(f"{result.title} {result.url}")
        
        # huatech搜索
        if huatech_results:
            if references:  # 如果前面有搜索结果，需要空行分隔
                references.append("")
            references.append("huatech搜索：")
            for result in huatech_results:
                references.append(f"{result.title} {result.url}")
        
        return "\n".join(references) if references else ""

    async def close(self):
        """关闭服务"""
        await self.client.close()


# 全局搜索服务实例
web_search_service = WebSearchService()
