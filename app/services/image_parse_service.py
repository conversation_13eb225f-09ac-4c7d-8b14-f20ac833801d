"""
图片解析服务
"""
import aiohttp
import json
import base64
from typing import Dict, Any, Optional
from dataclasses import dataclass

from app.config import settings
from app.services.llm_service import LLMClient
from app.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ImageParseRequest:
    """图片解析请求数据结构"""
    url: str
    question: str = "描述这个图片"  # 默认问题，可以自定义


@dataclass
class ImageParseResult:
    """图片解析结果数据结构"""
    success: bool
    output: str
    message: str


class ImageParseClient:
    """图片解析客户端 - 使用LLM视觉模型"""

    def __init__(self):
        self.session = None
        self._timeout = aiohttp.ClientTimeout(total=180)  # 3分钟超时
        self.vision_model = "ht::saas-doubao-1.5-vl-pro-32k"  # 强制使用指定的视觉模型
        
        # 为图片解析创建专用的LLM客户端，使用专门的API地址
        self.vision_api_url = "http://168.64.26.85/web/unauth/LLM_api_proxy/v1/chat/completions"
        self.llm_client = LLMClient(
            api_url=self.vision_api_url,
            default_model=self.vision_model
        )
        logger.info(f"初始化图片解析专用LLM客户端: {self.vision_api_url}")

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP会话"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self._timeout)
        return self.session

    async def _download_image_to_base64(self, url: str) -> str:
        """下载图片并转换为base64编码"""
        session = await self._get_session()
        
        try:
            logger.info(f"开始下载图片: {url}")
            async with session.get(url) as response:
                if response.status != 200:
                    raise Exception(f"图片下载失败: HTTP {response.status}")
                
                image_data = await response.read()
                # 将图片数据编码为base64
                base64_data = base64.b64encode(image_data).decode('utf-8')
                # 获取内容类型
                content_type = response.headers.get('content-type', 'image/jpeg')
                # 构造data URL格式
                base64_url = f"data:{content_type};base64,{base64_data}"
                
                logger.info(f"图片下载并转换为base64成功，大小: {len(base64_data)} bytes")
                return base64_url
                
        except Exception as e:
            logger.error(f"图片下载失败: {str(e)}")
            raise

    async def parse_image(self, request: ImageParseRequest) -> ImageParseResult:
        """使用LLM视觉模型执行图片解析"""
        try:
            logger.info(f"开始使用LLM解析图片: {request.url}")
            
            # 将图片URL转换为base64
            base64_image = await self._download_image_to_base64(request.url)
            
            # 构造LLM请求消息，格式按照用户要求
            messages = [
                {
                    "role": "system",
                    "content": """**角色 (Role):**
你是一个专业的多模态AI助手，专门处理和分析用户提供的图片。你的核心能力包括：精准的图片内容描述（图像解释）、可靠的文字提取（OCR）、以及基于图片内容的常识性问答和创意生成。你必须严格遵循事实，仅基于图片中的可见信息进行回应。

**能力与工作流程 (Capabilities & Workflow):**

1.  **图像解释 (Image Captioning & Description):**
    *   对图片的场景、主体对象、颜色、布局、氛围、可能隐含的动作或情感进行详细、客观且有条理的描述。
    *   可以推断合理的常识（例如，推断"阳光下的人影"意味着"天气晴朗"），但必须明确区分"所见"与"推测"，避免过度解读。

2.  **文字提取 (Optical Character Recognition - OCR):**
    *   **核心原则：当且仅当图片中存在清晰可辨的文字时，才进行提取。**
    *   准确识别并提取图片中的所有文字内容，包括但不限于文档、海报、标志、屏幕截图、自然场景中的文字（如店铺招牌）等。
    *   **绝对禁止的行为：如果图片中没有任何文字，你必须直接、明确地告知用户"经识别，该图片中未发现任何文字内容"或类似表述。严禁编造、臆测或生成图片中不存在的文字信息。**
    *   提取后，可以根据用户要求对文字进行翻译、总结或解释，但原始文本必须准确无误。

3.  **通用问答与创意 (Q&A & Creativity):**
    *   回答用户关于图片内容的疑问（例如，"这个人的穿着风格是什么？"、"这张照片可能是在哪里拍的？"）。
    *   基于图片内容进行合理的创意发挥，如生成故事、诗歌、广告文案等，但需明确说明这是创意作品。

**约束与准则 (Constraints & Guidelines):**

*   **诚实与准确 (Honesty & Accuracy):** 对你"看到"的内容保持高度诚实。如果图片模糊、难以辨认或无法确定内容，应如实告知用户不确定性。
*   **无文字处理 (No-Text Handling):** 这是硬性要求。对于纯图片（如风景照、艺术品、无文字的图表），你的文字提取响应必须是"未发现文字"。不得使用"可能"、"也许"、"似乎有"等模糊表述，必须给出确定性结论。
*   **隐私与伦理 (Privacy & Ethics):** 不得识别或评论图片中可能涉及的个人敏感信息（如人脸、车牌号）。拒绝从事任何违法、不道德或有害的图片分析请求。"""
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": request.question
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": base64_image
                            }
                        }
                    ]
                }
            ]
            
            logger.info(f"=== LLM图片解析请求调试信息 ===")
            logger.info(f"模型: {self.vision_model}")
            logger.info(f"消息结构: {json.dumps(messages, ensure_ascii=False, indent=2)[:500]}...")
            
            # 调用专用的LLM客户端
            from app.services.llm_service import LLMRequest
            llm_request = LLMRequest(
                messages=messages,
                model=self.vision_model,
                max_tokens=2000,
                temperature=0.7,
                stream=False
            )
            llm_response = await self.llm_client.chat_completion(llm_request)
            
            logger.info(f"=== LLM响应调试信息 ===")
            logger.info(f"响应类型: {type(llm_response)}")
            logger.info(f"响应内容: {json.dumps(llm_response, ensure_ascii=False, indent=2)[:500]}...")
            
            # 解析LLM响应
            parsed_result = self._parse_llm_response(llm_response)
            logger.info(f"LLM图片解析完成: success={parsed_result.success}")
            return parsed_result
                    
        except Exception as e:
            logger.error(f"LLM图片解析执行异常: {str(e)}", exc_info=True)
            return ImageParseResult(
                success=False,
                output="",
                message=f"LLM解析异常: {str(e)}"
            )

    def _parse_llm_response(self, response: Dict[str, Any]) -> ImageParseResult:
        """解析LLM响应为标准格式"""
        logger.info(f"=== 开始解析LLM响应结果 ===")
        logger.info(f"响应类型: {type(response)}")
        logger.info(f"响应键: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        
        try:
            # 标准的LLM chat completion响应格式
            if "choices" in response and isinstance(response["choices"], list) and len(response["choices"]) > 0:
                choice = response["choices"][0]
                
                # 检查是否有错误
                if "error" in response:
                    error_info = response["error"]
                    error_message = error_info.get("message", "未知错误") if isinstance(error_info, dict) else str(error_info)
                    logger.error(f"LLM响应包含错误: {error_message}")
                    return ImageParseResult(
                        success=False,
                        output="",
                        message=f"LLM处理错误: {error_message}"
                    )
                
                # 提取消息内容
                if "message" in choice and "content" in choice["message"]:
                    content = choice["message"]["content"]
                    logger.info(f"成功提取LLM响应内容，长度: {len(content)}")
                    
                    return ImageParseResult(
                        success=True,
                        output=content,
                        message="图片解析成功"
                    )
                else:
                    logger.warning("LLM响应中没有找到message.content字段")
                    return ImageParseResult(
                        success=False,
                        output="",
                        message="LLM响应格式异常：缺少content字段"
                    )
            
            # 检查是否是错误响应
            elif "error" in response:
                error_info = response["error"]
                error_message = error_info.get("message", "未知错误") if isinstance(error_info, dict) else str(error_info)
                logger.error(f"LLM返回错误响应: {error_message}")
                return ImageParseResult(
                    success=False,
                    output="",
                    message=f"LLM服务错误: {error_message}"
                )
            
            else:
                logger.warning("LLM响应格式不符合预期")
                logger.warning(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
                return ImageParseResult(
                    success=False,
                    output="",
                    message="LLM响应格式异常"
                )
                        
        except Exception as e:
            logger.error(f"解析LLM响应失败: {str(e)}", exc_info=True)
            logger.error(f"响应内容: {json.dumps(response, ensure_ascii=False, indent=2)}")
            return ImageParseResult(
                success=False,
                output="",
                message=f"响应解析异常: {str(e)}"
            )

    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
        
        # 关闭专用的LLM客户端
        if hasattr(self, 'llm_client') and self.llm_client:
            await self.llm_client.close()


class ImageParseService:
    """图片解析服务管理器 - 使用LLM视觉模型"""

    def __init__(self):
        self.client = ImageParseClient()
        logger.info("初始化图片解析服务 - 使用LLM视觉模型")

    async def parse_image(self, url: str, question: str = "描述这个图片") -> ImageParseResult:
        """执行图片解析"""
        logger.info(f"=== ImageParseService.parse_image 调用 ===")
        logger.info(f"图片URL: {url}")
        logger.info(f"问题: {question}")
        
        request = ImageParseRequest(url=url, question=question)
        
        result = await self.client.parse_image(request)
        logger.info(f"ImageParseService.parse_image 返回结果: success={result.success}")
        return result

    async def close(self):
        """关闭服务"""
        await self.client.close()


# 全局图片解析服务实例
image_parse_service = ImageParseService()
