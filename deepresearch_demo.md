# 🧠 DeepResearch 功能演示

## 🚀 功能说明

现在你的通用问答智能体已经具备了 **DeepResearch 能力**！当启用 `searchOptions` 时，LLM 可以智能地决定何时调用搜索工具来获取最新、准确的信息。

## 🎯 核心特性

### 1. **LLM 自主决策**
- LLM 根据问题内容智能判断是否需要搜索
- 支持多轮搜索：可以根据初步结果决定是否需要进一步搜索
- 无需预设规则，完全由 AI 智能决策

### 2. **工具调用架构**  
- 使用 OpenAI Function Calling 格式
- 类似 MCP（Model Context Protocol）的工具调用
- 支持扩展更多工具类型

### 3. **无缝集成**
- 保持原有 `/api/v1/default` 端点不变
- 向后兼容，不影响现有功能
- 只需通过 `searchOptions` 启用即可

## 🔧 API 使用方法

### 普通对话（不启用搜索）
```bash
curl -X POST "http://localhost:8000/api/v1/default" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "什么是机器学习",
    "stream": false
  }'
```

### DeepResearch 模式（启用搜索工具）
```bash
curl -X POST "http://localhost:8000/api/v1/default" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "2024年最新的人工智能突破是什么",
    "stream": false,
    "searchOptions": {
      "enableInternetSearch": true,
      "provider": ["hiagent"],
      "authInfos": {
        "wiki": {
          "cookie": "your_wiki_auth_cookie"
        }
      }
    }
  }'
```

### 流式响应 + DeepResearch
```bash
curl -X POST "http://localhost:8000/api/v1/default" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "最新的iPhone价格是多少",
    "stream": true,
    "searchOptions": {
      "enableInternetSearch": true,
      "provider": ["hiagent", "wiki"]
    }
  }'
```

## 🎮 测试场景

### 应该触发搜索的问题
- "最新的科技新闻有哪些？"
- "今天的天气怎么样？"
- "iPhone 15的价格是多少？"
- "2024年奥运会的最新消息"
- "ChatGPT最新版本有什么功能？"

### 不应该触发搜索的问题  
- "什么是机器学习？"
- "如何学习编程？"
- "你好，今天过得怎么样？"
- "解释一下量子力学"
- "1+1等于多少？"

## 📊 工作流程

```mermaid
graph TD
    A[用户提问] --> B{启用searchOptions?}
    B -->|否| C[普通对话模式]
    B -->|是| D[增强系统提示]
    D --> E[LLM分析问题]
    E --> F{需要搜索?}
    F -->|否| G[直接回答]
    F -->|是| H[调用搜索工具]
    H --> I[处理搜索结果]
    I --> J[LLM综合分析]
    J --> K[提供最终答案]
```

## 🛠️ 技术架构

### 工具定义 (OpenAI Function Schema)
```json
{
  "type": "function",
  "function": {
    "name": "web_search",
    "description": "联网搜索工具。当需要获取最新信息、实时数据、具体事实等时使用。",
    "parameters": {
      "type": "object",
      "properties": {
        "query": {
          "type": "string",
          "description": "搜索关键词"
        },
        "provider": {
          "type": "array",
          "items": {"type": "string", "enum": ["hiagent", "wiki"]},
          "description": "搜索引擎提供商"
        }
      },
      "required": ["query"]
    }
  }
}
```

### LangGraph 工作流
1. **validate_input** → 验证输入和搜索配置
2. **process_single_question** → 构建增强系统提示
3. **call_llm_with_tools** → 调用LLM（支持工具）
4. **execute_tools** → 执行LLM请求的工具
5. **finalize** → 输出最终结果

## 🌟 优势特点

### 智能化程度更高
- **旧版本**: 关键词匹配 → 容易误判
- **新版本**: LLM智能判断 → 更准确、更灵活

### 多轮研究能力
- LLM可以根据初步搜索结果决定是否需要进一步搜索
- 支持递归式深度研究
- 自适应搜索策略

### 向后兼容
- 现有API端点保持不变
- 不影响原有功能
- 平滑升级体验

## 🎯 下一步改进

1. **增加更多工具类型**
   - 计算器工具
   - 代码执行工具  
   - 图像生成工具

2. **优化工具调用策略**
   - 并行工具调用
   - 工具链组合
   - 结果缓存机制

3. **增强提示工程**
   - 更精确的工具使用指导
   - 情境感知的搜索策略
   - 个性化工具偏好

---
